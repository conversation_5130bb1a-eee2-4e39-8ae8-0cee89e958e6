{"name": "@workspace/ui", "version": "0.0.0", "type": "module", "private": true, "scripts": {"lint": "eslint . --max-warnings 0"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/typography": "^0.5.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "0.522.0", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@turbo/gen": "^2.5.4", "@types/node": "^24.0.3", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.14"}, "exports": {"./globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./tailwind.config": "./tailwind.config.ts", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts"}}