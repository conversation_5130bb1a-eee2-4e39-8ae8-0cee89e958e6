/**
 * ---------------------------------------------------------------------------------
 * This file has been generated by Sanity TypeGen.
 * Command: `sanity typegen generate`
 *
 * Any modifications made directly to this file will be overwritten the next time
 * the TypeScript definitions are generated. Please make changes to the Sanity
 * schema definitions and/or GROQ queries if you need to update these types.
 *
 * For more information on how to use Sanity TypeGen, visit the official documentation:
 * https://www.sanity.io/docs/sanity-typegen
 * ---------------------------------------------------------------------------------
 */

// Source: schema.json
export type SanityImagePaletteSwatch = {
  _type: "sanity.imagePaletteSwatch";
  background?: string;
  foreground?: string;
  population?: number;
  title?: string;
};

export type SanityImagePalette = {
  _type: "sanity.imagePalette";
  darkMuted?: SanityImagePaletteSwatch;
  lightVibrant?: SanityImagePaletteSwatch;
  darkVibrant?: SanityImagePaletteSwatch;
  vibrant?: SanityImagePaletteSwatch;
  dominant?: SanityImagePaletteSwatch;
  lightMuted?: SanityImagePaletteSwatch;
  muted?: SanityImagePaletteSwatch;
};

export type SanityImageDimensions = {
  _type: "sanity.imageDimensions";
  height?: number;
  width?: number;
  aspectRatio?: number;
};

export type SanityFileAsset = {
  _id: string;
  _type: "sanity.fileAsset";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  originalFilename?: string;
  label?: string;
  title?: string;
  description?: string;
  altText?: string;
  sha1hash?: string;
  extension?: string;
  mimeType?: string;
  size?: number;
  assetId?: string;
  uploadId?: string;
  path?: string;
  url?: string;
  source?: SanityAssetSourceData;
};

export type Geopoint = {
  _type: "geopoint";
  lat?: number;
  lng?: number;
  alt?: number;
};

export type SubscribeNewsletter = {
  _type: "subscribeNewsletter";
  title?: string;
  subTitle?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h2" | "h3" | "h4" | "h5" | "h6" | "inline";
    listItem?: "number" | "bullet";
    markDefs?: Array<{
      customLink?: CustomUrl;
      _type: "customLink";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  helperText?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h2" | "h3" | "h4" | "h5" | "h6" | "inline";
    listItem?: "number" | "bullet";
    markDefs?: Array<{
      customLink?: CustomUrl;
      _type: "customLink";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
};

export type ImageLinkCards = {
  _type: "imageLinkCards";
  eyebrow?: string;
  title: string;
  richText?: Array<
    | {
        children?: Array<{
          marks?: Array<string>;
          text?: string;
          _type: "span";
          _key: string;
        }>;
        style?: "normal" | "h2" | "h3" | "h4" | "h5" | "h6" | "inline";
        listItem?: "number" | "bullet";
        markDefs?: Array<{
          customLink?: CustomUrl;
          _type: "customLink";
          _key: string;
        }>;
        level?: number;
        _type: "block";
        _key: string;
      }
    | {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        caption?: string;
        _type: "image";
        _key: string;
      }
  >;
  buttons?: Array<
    {
      _key: string;
    } & Button
  >;
  cards?: Array<{
    title: string;
    description: string;
    image?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    url?: CustomUrl;
    _type: "imageLinkCard";
    _key: string;
  }>;
};

export type FaqAccordion = {
  _type: "faqAccordion";
  eyebrow?: string;
  title: string;
  subtitle?: string;
  link?: {
    title?: string;
    description?: string;
    url?: CustomUrl;
  };
  faqs: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "faq";
  }>;
};

export type FeatureCardsIcon = {
  _type: "featureCardsIcon";
  eyebrow?: string;
  title?: string;
  richText?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h2" | "h3" | "h4" | "h5" | "h6" | "inline";
    listItem?: "number" | "bullet";
    markDefs?: Array<{
      customLink?: CustomUrl;
      _type: "customLink";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  cards?: Array<{
    icon?: IconPicker;
    title?: string;
    richText?: Array<{
      children?: Array<{
        marks?: Array<string>;
        text?: string;
        _type: "span";
        _key: string;
      }>;
      style?: "normal" | "h2" | "h3" | "h4" | "h5" | "h6" | "inline";
      listItem?: "number" | "bullet";
      markDefs?: Array<{
        customLink?: CustomUrl;
        _type: "customLink";
        _key: string;
      }>;
      level?: number;
      _type: "block";
      _key: string;
    }>;
    _type: "featureCardIcon";
    _key: string;
  }>;
};

export type Cta = {
  _type: "cta";
  eyebrow?: string;
  title?: string;
  richText?: Array<
    | {
        children?: Array<{
          marks?: Array<string>;
          text?: string;
          _type: "span";
          _key: string;
        }>;
        style?: "normal" | "h2" | "h3" | "h4" | "h5" | "h6" | "inline";
        listItem?: "number" | "bullet";
        markDefs?: Array<{
          customLink?: CustomUrl;
          _type: "customLink";
          _key: string;
        }>;
        level?: number;
        _type: "block";
        _key: string;
      }
    | {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        caption?: string;
        _type: "image";
        _key: string;
      }
  >;
  buttons?: Array<
    {
      _key: string;
    } & Button
  >;
};

export type Hero = {
  _type: "hero";
  badge?: string;
  title?: string;
  richText?: Array<
    | {
        children?: Array<{
          marks?: Array<string>;
          text?: string;
          _type: "span";
          _key: string;
        }>;
        style?: "normal" | "h2" | "h3" | "h4" | "h5" | "h6" | "inline";
        listItem?: "number" | "bullet";
        markDefs?: Array<{
          customLink?: CustomUrl;
          _type: "customLink";
          _key: string;
        }>;
        level?: number;
        _type: "block";
        _key: string;
      }
    | {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        caption?: string;
        _type: "image";
        _key: string;
      }
  >;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  buttons?: Array<
    {
      _key: string;
    } & Button
  >;
};

export type PageBuilder = Array<
  | ({
      _key: string;
    } & Hero)
  | ({
      _key: string;
    } & Cta)
  | ({
      _key: string;
    } & FeatureCardsIcon)
  | ({
      _key: string;
    } & FaqAccordion)
  | ({
      _key: string;
    } & ImageLinkCards)
  | ({
      _key: string;
    } & SubscribeNewsletter)
>;

export type Button = {
  _type: "button";
  variant?: "default" | "secondary" | "outline" | "link";
  text?: string;
  url?: CustomUrl;
};

export type RichText = Array<
  | {
      children?: Array<{
        marks?: Array<string>;
        text?: string;
        _type: "span";
        _key: string;
      }>;
      style?: "normal" | "h2" | "h3" | "h4" | "h5" | "h6" | "inline";
      listItem?: "number" | "bullet";
      markDefs?: Array<{
        customLink?: CustomUrl;
        _type: "customLink";
        _key: string;
      }>;
      level?: number;
      _type: "block";
      _key: string;
    }
  | {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      caption?: string;
      _type: "image";
      _key: string;
    }
>;

export type Navbar = {
  _id: string;
  _type: "navbar";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  label: string;
  columns?: Array<
    | {
        title?: string;
        links: Array<{
          icon?: IconPicker;
          name?: string;
          description?: string;
          url?: CustomUrl;
          _type: "navbarColumnLink";
          _key: string;
        }>;
        _type: "navbarColumn";
        _key: string;
      }
    | {
        name?: string;
        url?: CustomUrl;
        _type: "navbarLink";
        _key: string;
      }
  >;
  buttons?: Array<
    {
      _key: string;
    } & Button
  >;
};

export type Footer = {
  _id: string;
  _type: "footer";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  label: string;
  subtitle?: string;
  columns?: Array<{
    title?: string;
    links?: Array<{
      name?: string;
      url?: CustomUrl;
      _type: "footerColumnLink";
      _key: string;
    }>;
    _type: "footerColumn";
    _key: string;
  }>;
};

export type Settings = {
  _id: string;
  _type: "settings";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  label: string;
  siteTitle: string;
  siteDescription: string;
  logo?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  contactEmail?: string;
  socialLinks?: {
    linkedin?: string;
    facebook?: string;
    twitter?: string;
    instagram?: string;
    youtube?: string;
  };
};

export type BlogIndex = {
  _id: string;
  _type: "blogIndex";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  description?: string;
  slug: Slug;
  displayFeaturedBlogs?: "yes" | "no";
  featuredBlogsCount?: "1" | "2" | "3";
  pageBuilder?: PageBuilder;
  seoTitle?: string;
  seoDescription?: string;
  seoImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  ogTitle?: string;
  ogDescription?: string;
};

export type HomePage = {
  _id: string;
  _type: "homePage";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  description?: string;
  slug: Slug;
  pageBuilder?: PageBuilder;
  seoTitle?: string;
  seoDescription?: string;
  seoImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  ogTitle?: string;
  ogDescription?: string;
};

export type Author = {
  _id: string;
  _type: "author";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name: string;
  position?: string;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  bio?: string;
};

export type Faq = {
  _id: string;
  _type: "faq";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title: string;
  richText?: RichText;
};

export type Page = {
  _id: string;
  _type: "page";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title: string;
  description?: string;
  slug: Slug;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  pageBuilder?: PageBuilder;
  seoTitle?: string;
  seoDescription?: string;
  seoImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  seoNoIndex?: boolean;
  ogTitle?: string;
  ogDescription?: string;
};

export type Blog = {
  _id: string;
  _type: "blog";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  orderRank?: string;
  title: string;
  description?: string;
  slug: Slug;
  authors: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "author";
  }>;
  publishedAt?: string;
  image: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  richText?: RichText;
  seoTitle?: string;
  seoDescription?: string;
  seoImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  seoNoIndex?: boolean;
  seoHideFromLists?: boolean;
  ogTitle?: string;
  ogDescription?: string;
};

export type CustomUrl = {
  _type: "customUrl";
  type: "internal" | "external";
  openInNewTab?: boolean;
  external?: string;
  href?: string;
  internal?:
    | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "blog";
      }
    | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "blogIndex";
      }
    | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "page";
      };
};

export type SanityImageCrop = {
  _type: "sanity.imageCrop";
  top?: number;
  bottom?: number;
  left?: number;
  right?: number;
};

export type SanityImageHotspot = {
  _type: "sanity.imageHotspot";
  x?: number;
  y?: number;
  height?: number;
  width?: number;
};

export type SanityImageAsset = {
  _id: string;
  _type: "sanity.imageAsset";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  originalFilename?: string;
  label?: string;
  title?: string;
  description?: string;
  altText?: string;
  sha1hash?: string;
  extension?: string;
  mimeType?: string;
  size?: number;
  assetId?: string;
  uploadId?: string;
  path?: string;
  url?: string;
  metadata?: SanityImageMetadata;
  source?: SanityAssetSourceData;
};

export type SanityAssetSourceData = {
  _type: "sanity.assetSourceData";
  name?: string;
  id?: string;
  url?: string;
};

export type SanityImageMetadata = {
  _type: "sanity.imageMetadata";
  location?: Geopoint;
  dimensions?: SanityImageDimensions;
  palette?: SanityImagePalette;
  lqip?: string;
  blurHash?: string;
  hasAlpha?: boolean;
  isOpaque?: boolean;
};

export type MediaTag = {
  _id: string;
  _type: "media.tag";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: Slug;
};

export type Slug = {
  _type: "slug";
  current: string;
  source?: string;
};

export type IconPicker = {
  _type: "iconPicker";
  provider?: string;
  name?: string;
  svg?: string;
};

export type SanityAssistInstructionTask = {
  _type: "sanity.assist.instructionTask";
  path?: string;
  instructionKey?: string;
  started?: string;
  updated?: string;
  info?: string;
};

export type SanityAssistTaskStatus = {
  _type: "sanity.assist.task.status";
  tasks?: Array<
    {
      _key: string;
    } & SanityAssistInstructionTask
  >;
};

export type SanityAssistSchemaTypeAnnotations = {
  _type: "sanity.assist.schemaType.annotations";
  title?: string;
  fields?: Array<
    {
      _key: string;
    } & SanityAssistSchemaTypeField
  >;
};

export type SanityAssistOutputType = {
  _type: "sanity.assist.output.type";
  type?: string;
};

export type SanityAssistOutputField = {
  _type: "sanity.assist.output.field";
  path?: string;
};

export type SanityAssistInstructionContext = {
  _type: "sanity.assist.instruction.context";
  reference: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "assist.instruction.context";
  };
};

export type AssistInstructionContext = {
  _id: string;
  _type: "assist.instruction.context";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  context?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal";
    listItem?: never;
    markDefs?: null;
    level?: number;
    _type: "block";
    _key: string;
  }>;
};

export type SanityAssistInstructionUserInput = {
  _type: "sanity.assist.instruction.userInput";
  message: string;
  description?: string;
};

export type SanityAssistInstructionPrompt = Array<{
  children?: Array<
    | {
        marks?: Array<string>;
        text?: string;
        _type: "span";
        _key: string;
      }
    | ({
        _key: string;
      } & SanityAssistInstructionFieldRef)
    | ({
        _key: string;
      } & SanityAssistInstructionContext)
    | ({
        _key: string;
      } & SanityAssistInstructionUserInput)
  >;
  style?: "normal";
  listItem?: never;
  markDefs?: null;
  level?: number;
  _type: "block";
  _key: string;
}>;

export type SanityAssistInstructionFieldRef = {
  _type: "sanity.assist.instruction.fieldRef";
  path?: string;
};

export type SanityAssistInstruction = {
  _type: "sanity.assist.instruction";
  prompt?: SanityAssistInstructionPrompt;
  icon?: string;
  title?: string;
  userId?: string;
  createdById?: string;
  output?: Array<
    | ({
        _key: string;
      } & SanityAssistOutputField)
    | ({
        _key: string;
      } & SanityAssistOutputType)
  >;
};

export type SanityAssistSchemaTypeField = {
  _type: "sanity.assist.schemaType.field";
  path?: string;
  instructions?: Array<
    {
      _key: string;
    } & SanityAssistInstruction
  >;
};

export type AllSanitySchemaTypes =
  | SanityImagePaletteSwatch
  | SanityImagePalette
  | SanityImageDimensions
  | SanityFileAsset
  | Geopoint
  | SubscribeNewsletter
  | ImageLinkCards
  | FaqAccordion
  | FeatureCardsIcon
  | Cta
  | Hero
  | PageBuilder
  | Button
  | RichText
  | Navbar
  | Footer
  | Settings
  | BlogIndex
  | HomePage
  | Author
  | Faq
  | Page
  | Blog
  | CustomUrl
  | SanityImageCrop
  | SanityImageHotspot
  | SanityImageAsset
  | SanityAssetSourceData
  | SanityImageMetadata
  | MediaTag
  | Slug
  | IconPicker
  | SanityAssistInstructionTask
  | SanityAssistTaskStatus
  | SanityAssistSchemaTypeAnnotations
  | SanityAssistOutputType
  | SanityAssistOutputField
  | SanityAssistInstructionContext
  | AssistInstructionContext
  | SanityAssistInstructionUserInput
  | SanityAssistInstructionPrompt
  | SanityAssistInstructionFieldRef
  | SanityAssistInstruction
  | SanityAssistSchemaTypeField;
export declare const internalGroqTypeReferenceTo: unique symbol;
// Source: ../web/src/lib/sanity/query.ts
// Variable: queryImageType
// Query: *[_type == "page" && defined(image)][0]{      image{    ...,    ...asset->{      "alt": coalesce(altText, originalFilename, "no-alt"),      "blurData": metadata.lqip,      "dominantColor": metadata.palette.dominant.background    },  }  }.image
export type QueryImageTypeResult =
  | {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
      alt: string | "no-alt";
      blurData: string | null;
      dominantColor: string | null;
    }
  | {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    }
  | null;
// Variable: queryHomePageData
// Query: *[_type == "homePage" && _id == "homePage"][0]{    ...,    _id,    _type,    "slug": slug.current,    title,    description,      pageBuilder[]{    ...,    _type,      _type == "cta" => {    ...,      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  },      buttons[]{    text,    variant,    _key,    _type,    "openInNewTab": url.openInNewTab,    "href": select(      url.type == "internal" => url.internal->slug.current,      url.type == "external" => url.external,      url.href    ),  },  },      _type == "hero" => {    ...,      image{    ...,    ...asset->{      "alt": coalesce(altText, originalFilename, "no-alt"),      "blurData": metadata.lqip,      "dominantColor": metadata.palette.dominant.background    },  },      buttons[]{    text,    variant,    _key,    _type,    "openInNewTab": url.openInNewTab,    "href": select(      url.type == "internal" => url.internal->slug.current,      url.type == "external" => url.external,      url.href    ),  },      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  }  },      _type == "faqAccordion" => {    ...,      "faqs": array::compact(faqs[]->{    title,    _id,    _type,      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  }  }),    link{      ...,      "openInNewTab": url.openInNewTab,      "href": select(        url.type == "internal" => url.internal->slug.current,        url.type == "external" => url.external,        url.href      )    }  },      _type == "featureCardsIcon" => {    ...,      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  },    "cards": array::compact(cards[]{      ...,        richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  },    })  },      _type == "subscribeNewsletter" => {    ...,    "subTitle": subTitle[]{      ...,        markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }    },    "helperText": helperText[]{      ...,        markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }    }  },      _type == "imageLinkCards" => {    ...,      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  },      buttons[]{    text,    variant,    _key,    _type,    "openInNewTab": url.openInNewTab,    "href": select(      url.type == "internal" => url.internal->slug.current,      url.type == "external" => url.external,      url.href    ),  },    "cards": array::compact(cards[]{      ...,      "openInNewTab": url.openInNewTab,      "href": select(        url.type == "internal" => url.internal->slug.current,        url.type == "external" => url.external,        url.href      ),        image{    ...,    ...asset->{      "alt": coalesce(altText, originalFilename, "no-alt"),      "blurData": metadata.lqip,      "dominantColor": metadata.palette.dominant.background    },  },    })  }  }  }
export type QueryHomePageDataResult = {
  _id: string;
  _type: "homePage";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title: string | null;
  description: string | null;
  slug: string;
  pageBuilder: Array<
    | {
        _key: string;
        _type: "cta";
        eyebrow?: string;
        title?: string;
        richText: Array<
          | {
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: "span";
                _key: string;
              }>;
              style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
              listItem?: "bullet" | "number";
              markDefs: Array<
                | {
                    customLink?: CustomUrl;
                    _type: "customLink";
                    _key: string;
                    openInNewTab: boolean | null;
                    href: string | "#" | null;
                  }
                | {
                    customLink?: CustomUrl;
                    _type: "customLink";
                    _key: string;
                  }
              > | null;
              level?: number;
              _type: "block";
              _key: string;
            }
          | {
              asset?: {
                _ref: string;
                _type: "reference";
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              caption?: string;
              _type: "image";
              _key: string;
              markDefs: null;
            }
        > | null;
        buttons: Array<{
          text: string | null;
          variant: "default" | "link" | "outline" | "secondary" | null;
          _key: string;
          _type: "button";
          openInNewTab: boolean | null;
          href: string | null;
        }> | null;
      }
    | {
        _key: string;
        _type: "faqAccordion";
        eyebrow?: string;
        title: string;
        subtitle?: string;
        link: {
          title?: string;
          description?: string;
          url?: CustomUrl;
          openInNewTab: boolean | null;
          href: string | null;
        } | null;
        faqs: Array<{
          title: string;
          _id: string;
          _type: "faq";
          richText: Array<
            | {
                children?: Array<{
                  marks?: Array<string>;
                  text?: string;
                  _type: "span";
                  _key: string;
                }>;
                style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
                listItem?: "bullet" | "number";
                markDefs: Array<
                  | {
                      customLink?: CustomUrl;
                      _type: "customLink";
                      _key: string;
                      openInNewTab: boolean | null;
                      href: string | "#" | null;
                    }
                  | {
                      customLink?: CustomUrl;
                      _type: "customLink";
                      _key: string;
                    }
                > | null;
                level?: number;
                _type: "block";
                _key: string;
              }
            | {
                asset?: {
                  _ref: string;
                  _type: "reference";
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                caption?: string;
                _type: "image";
                _key: string;
                markDefs: null;
              }
          > | null;
        }>;
      }
    | {
        _key: string;
        _type: "featureCardsIcon";
        eyebrow?: string;
        title?: string;
        richText: Array<{
          children?: Array<{
            marks?: Array<string>;
            text?: string;
            _type: "span";
            _key: string;
          }>;
          style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
          listItem?: "bullet" | "number";
          markDefs: Array<
            | {
                customLink?: CustomUrl;
                _type: "customLink";
                _key: string;
                openInNewTab: boolean | null;
                href: string | "#" | null;
              }
            | {
                customLink?: CustomUrl;
                _type: "customLink";
                _key: string;
              }
          > | null;
          level?: number;
          _type: "block";
          _key: string;
        }> | null;
        cards: Array<{
          icon?: IconPicker;
          title?: string;
          richText: Array<{
            children?: Array<{
              marks?: Array<string>;
              text?: string;
              _type: "span";
              _key: string;
            }>;
            style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
            listItem?: "bullet" | "number";
            markDefs: Array<
              | {
                  customLink?: CustomUrl;
                  _type: "customLink";
                  _key: string;
                  openInNewTab: boolean | null;
                  href: string | "#" | null;
                }
              | {
                  customLink?: CustomUrl;
                  _type: "customLink";
                  _key: string;
                }
            > | null;
            level?: number;
            _type: "block";
            _key: string;
          }> | null;
          _type: "featureCardIcon";
          _key: string;
        }> | null;
      }
    | {
        _key: string;
        _type: "hero";
        badge?: string;
        title?: string;
        richText: Array<
          | {
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: "span";
                _key: string;
              }>;
              style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
              listItem?: "bullet" | "number";
              markDefs: Array<
                | {
                    customLink?: CustomUrl;
                    _type: "customLink";
                    _key: string;
                    openInNewTab: boolean | null;
                    href: string | "#" | null;
                  }
                | {
                    customLink?: CustomUrl;
                    _type: "customLink";
                    _key: string;
                  }
              > | null;
              level?: number;
              _type: "block";
              _key: string;
            }
          | {
              asset?: {
                _ref: string;
                _type: "reference";
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              caption?: string;
              _type: "image";
              _key: string;
              markDefs: null;
            }
        > | null;
        image:
          | {
              asset?: {
                _ref: string;
                _type: "reference";
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: "image";
              alt: string | "no-alt";
              blurData: string | null;
              dominantColor: string | null;
            }
          | {
              asset?: {
                _ref: string;
                _type: "reference";
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: "image";
            }
          | null;
        buttons: Array<{
          text: string | null;
          variant: "default" | "link" | "outline" | "secondary" | null;
          _key: string;
          _type: "button";
          openInNewTab: boolean | null;
          href: string | null;
        }> | null;
      }
    | {
        _key: string;
        _type: "imageLinkCards";
        eyebrow?: string;
        title: string;
        richText: Array<
          | {
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: "span";
                _key: string;
              }>;
              style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
              listItem?: "bullet" | "number";
              markDefs: Array<
                | {
                    customLink?: CustomUrl;
                    _type: "customLink";
                    _key: string;
                    openInNewTab: boolean | null;
                    href: string | "#" | null;
                  }
                | {
                    customLink?: CustomUrl;
                    _type: "customLink";
                    _key: string;
                  }
              > | null;
              level?: number;
              _type: "block";
              _key: string;
            }
          | {
              asset?: {
                _ref: string;
                _type: "reference";
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              caption?: string;
              _type: "image";
              _key: string;
              markDefs: null;
            }
        > | null;
        buttons: Array<{
          text: string | null;
          variant: "default" | "link" | "outline" | "secondary" | null;
          _key: string;
          _type: "button";
          openInNewTab: boolean | null;
          href: string | null;
        }> | null;
        cards: Array<{
          title: string;
          description: string;
          image:
            | {
                asset?: {
                  _ref: string;
                  _type: "reference";
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: "image";
                alt: string | "no-alt";
                blurData: string | null;
                dominantColor: string | null;
              }
            | {
                asset?: {
                  _ref: string;
                  _type: "reference";
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: "image";
              }
            | null;
          url?: CustomUrl;
          _type: "imageLinkCard";
          _key: string;
          openInNewTab: boolean | null;
          href: string | null;
        }> | null;
      }
    | {
        _key: string;
        _type: "subscribeNewsletter";
        title?: string;
        subTitle: Array<{
          children?: Array<{
            marks?: Array<string>;
            text?: string;
            _type: "span";
            _key: string;
          }>;
          style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
          listItem?: "bullet" | "number";
          markDefs: Array<
            | {
                customLink?: CustomUrl;
                _type: "customLink";
                _key: string;
                openInNewTab: boolean | null;
                href: string | "#" | null;
              }
            | {
                customLink?: CustomUrl;
                _type: "customLink";
                _key: string;
              }
          > | null;
          level?: number;
          _type: "block";
          _key: string;
        }> | null;
        helperText: Array<{
          children?: Array<{
            marks?: Array<string>;
            text?: string;
            _type: "span";
            _key: string;
          }>;
          style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
          listItem?: "bullet" | "number";
          markDefs: Array<
            | {
                customLink?: CustomUrl;
                _type: "customLink";
                _key: string;
                openInNewTab: boolean | null;
                href: string | "#" | null;
              }
            | {
                customLink?: CustomUrl;
                _type: "customLink";
                _key: string;
              }
          > | null;
          level?: number;
          _type: "block";
          _key: string;
        }> | null;
      }
  > | null;
  seoTitle?: string;
  seoDescription?: string;
  seoImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  ogTitle?: string;
  ogDescription?: string;
} | null;
// Variable: querySlugPageData
// Query: *[_type == "page" && slug.current == $slug][0]{    ...,    "slug": slug.current,      pageBuilder[]{    ...,    _type,      _type == "cta" => {    ...,      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  },      buttons[]{    text,    variant,    _key,    _type,    "openInNewTab": url.openInNewTab,    "href": select(      url.type == "internal" => url.internal->slug.current,      url.type == "external" => url.external,      url.href    ),  },  },      _type == "hero" => {    ...,      image{    ...,    ...asset->{      "alt": coalesce(altText, originalFilename, "no-alt"),      "blurData": metadata.lqip,      "dominantColor": metadata.palette.dominant.background    },  },      buttons[]{    text,    variant,    _key,    _type,    "openInNewTab": url.openInNewTab,    "href": select(      url.type == "internal" => url.internal->slug.current,      url.type == "external" => url.external,      url.href    ),  },      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  }  },      _type == "faqAccordion" => {    ...,      "faqs": array::compact(faqs[]->{    title,    _id,    _type,      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  }  }),    link{      ...,      "openInNewTab": url.openInNewTab,      "href": select(        url.type == "internal" => url.internal->slug.current,        url.type == "external" => url.external,        url.href      )    }  },      _type == "featureCardsIcon" => {    ...,      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  },    "cards": array::compact(cards[]{      ...,        richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  },    })  },      _type == "subscribeNewsletter" => {    ...,    "subTitle": subTitle[]{      ...,        markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }    },    "helperText": helperText[]{      ...,        markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }    }  },      _type == "imageLinkCards" => {    ...,      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  },      buttons[]{    text,    variant,    _key,    _type,    "openInNewTab": url.openInNewTab,    "href": select(      url.type == "internal" => url.internal->slug.current,      url.type == "external" => url.external,      url.href    ),  },    "cards": array::compact(cards[]{      ...,      "openInNewTab": url.openInNewTab,      "href": select(        url.type == "internal" => url.internal->slug.current,        url.type == "external" => url.external,        url.href      ),        image{    ...,    ...asset->{      "alt": coalesce(altText, originalFilename, "no-alt"),      "blurData": metadata.lqip,      "dominantColor": metadata.palette.dominant.background    },  },    })  }  }  }
export type QuerySlugPageDataResult = {
  _id: string;
  _type: "page";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title: string;
  description?: string;
  slug: string;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  pageBuilder: Array<
    | {
        _key: string;
        _type: "cta";
        eyebrow?: string;
        title?: string;
        richText: Array<
          | {
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: "span";
                _key: string;
              }>;
              style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
              listItem?: "bullet" | "number";
              markDefs: Array<
                | {
                    customLink?: CustomUrl;
                    _type: "customLink";
                    _key: string;
                    openInNewTab: boolean | null;
                    href: string | "#" | null;
                  }
                | {
                    customLink?: CustomUrl;
                    _type: "customLink";
                    _key: string;
                  }
              > | null;
              level?: number;
              _type: "block";
              _key: string;
            }
          | {
              asset?: {
                _ref: string;
                _type: "reference";
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              caption?: string;
              _type: "image";
              _key: string;
              markDefs: null;
            }
        > | null;
        buttons: Array<{
          text: string | null;
          variant: "default" | "link" | "outline" | "secondary" | null;
          _key: string;
          _type: "button";
          openInNewTab: boolean | null;
          href: string | null;
        }> | null;
      }
    | {
        _key: string;
        _type: "faqAccordion";
        eyebrow?: string;
        title: string;
        subtitle?: string;
        link: {
          title?: string;
          description?: string;
          url?: CustomUrl;
          openInNewTab: boolean | null;
          href: string | null;
        } | null;
        faqs: Array<{
          title: string;
          _id: string;
          _type: "faq";
          richText: Array<
            | {
                children?: Array<{
                  marks?: Array<string>;
                  text?: string;
                  _type: "span";
                  _key: string;
                }>;
                style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
                listItem?: "bullet" | "number";
                markDefs: Array<
                  | {
                      customLink?: CustomUrl;
                      _type: "customLink";
                      _key: string;
                      openInNewTab: boolean | null;
                      href: string | "#" | null;
                    }
                  | {
                      customLink?: CustomUrl;
                      _type: "customLink";
                      _key: string;
                    }
                > | null;
                level?: number;
                _type: "block";
                _key: string;
              }
            | {
                asset?: {
                  _ref: string;
                  _type: "reference";
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                caption?: string;
                _type: "image";
                _key: string;
                markDefs: null;
              }
          > | null;
        }>;
      }
    | {
        _key: string;
        _type: "featureCardsIcon";
        eyebrow?: string;
        title?: string;
        richText: Array<{
          children?: Array<{
            marks?: Array<string>;
            text?: string;
            _type: "span";
            _key: string;
          }>;
          style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
          listItem?: "bullet" | "number";
          markDefs: Array<
            | {
                customLink?: CustomUrl;
                _type: "customLink";
                _key: string;
                openInNewTab: boolean | null;
                href: string | "#" | null;
              }
            | {
                customLink?: CustomUrl;
                _type: "customLink";
                _key: string;
              }
          > | null;
          level?: number;
          _type: "block";
          _key: string;
        }> | null;
        cards: Array<{
          icon?: IconPicker;
          title?: string;
          richText: Array<{
            children?: Array<{
              marks?: Array<string>;
              text?: string;
              _type: "span";
              _key: string;
            }>;
            style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
            listItem?: "bullet" | "number";
            markDefs: Array<
              | {
                  customLink?: CustomUrl;
                  _type: "customLink";
                  _key: string;
                  openInNewTab: boolean | null;
                  href: string | "#" | null;
                }
              | {
                  customLink?: CustomUrl;
                  _type: "customLink";
                  _key: string;
                }
            > | null;
            level?: number;
            _type: "block";
            _key: string;
          }> | null;
          _type: "featureCardIcon";
          _key: string;
        }> | null;
      }
    | {
        _key: string;
        _type: "hero";
        badge?: string;
        title?: string;
        richText: Array<
          | {
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: "span";
                _key: string;
              }>;
              style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
              listItem?: "bullet" | "number";
              markDefs: Array<
                | {
                    customLink?: CustomUrl;
                    _type: "customLink";
                    _key: string;
                    openInNewTab: boolean | null;
                    href: string | "#" | null;
                  }
                | {
                    customLink?: CustomUrl;
                    _type: "customLink";
                    _key: string;
                  }
              > | null;
              level?: number;
              _type: "block";
              _key: string;
            }
          | {
              asset?: {
                _ref: string;
                _type: "reference";
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              caption?: string;
              _type: "image";
              _key: string;
              markDefs: null;
            }
        > | null;
        image:
          | {
              asset?: {
                _ref: string;
                _type: "reference";
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: "image";
              alt: string | "no-alt";
              blurData: string | null;
              dominantColor: string | null;
            }
          | {
              asset?: {
                _ref: string;
                _type: "reference";
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: "image";
            }
          | null;
        buttons: Array<{
          text: string | null;
          variant: "default" | "link" | "outline" | "secondary" | null;
          _key: string;
          _type: "button";
          openInNewTab: boolean | null;
          href: string | null;
        }> | null;
      }
    | {
        _key: string;
        _type: "imageLinkCards";
        eyebrow?: string;
        title: string;
        richText: Array<
          | {
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: "span";
                _key: string;
              }>;
              style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
              listItem?: "bullet" | "number";
              markDefs: Array<
                | {
                    customLink?: CustomUrl;
                    _type: "customLink";
                    _key: string;
                    openInNewTab: boolean | null;
                    href: string | "#" | null;
                  }
                | {
                    customLink?: CustomUrl;
                    _type: "customLink";
                    _key: string;
                  }
              > | null;
              level?: number;
              _type: "block";
              _key: string;
            }
          | {
              asset?: {
                _ref: string;
                _type: "reference";
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              caption?: string;
              _type: "image";
              _key: string;
              markDefs: null;
            }
        > | null;
        buttons: Array<{
          text: string | null;
          variant: "default" | "link" | "outline" | "secondary" | null;
          _key: string;
          _type: "button";
          openInNewTab: boolean | null;
          href: string | null;
        }> | null;
        cards: Array<{
          title: string;
          description: string;
          image:
            | {
                asset?: {
                  _ref: string;
                  _type: "reference";
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: "image";
                alt: string | "no-alt";
                blurData: string | null;
                dominantColor: string | null;
              }
            | {
                asset?: {
                  _ref: string;
                  _type: "reference";
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: "image";
              }
            | null;
          url?: CustomUrl;
          _type: "imageLinkCard";
          _key: string;
          openInNewTab: boolean | null;
          href: string | null;
        }> | null;
      }
    | {
        _key: string;
        _type: "subscribeNewsletter";
        title?: string;
        subTitle: Array<{
          children?: Array<{
            marks?: Array<string>;
            text?: string;
            _type: "span";
            _key: string;
          }>;
          style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
          listItem?: "bullet" | "number";
          markDefs: Array<
            | {
                customLink?: CustomUrl;
                _type: "customLink";
                _key: string;
                openInNewTab: boolean | null;
                href: string | "#" | null;
              }
            | {
                customLink?: CustomUrl;
                _type: "customLink";
                _key: string;
              }
          > | null;
          level?: number;
          _type: "block";
          _key: string;
        }> | null;
        helperText: Array<{
          children?: Array<{
            marks?: Array<string>;
            text?: string;
            _type: "span";
            _key: string;
          }>;
          style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
          listItem?: "bullet" | "number";
          markDefs: Array<
            | {
                customLink?: CustomUrl;
                _type: "customLink";
                _key: string;
                openInNewTab: boolean | null;
                href: string | "#" | null;
              }
            | {
                customLink?: CustomUrl;
                _type: "customLink";
                _key: string;
              }
          > | null;
          level?: number;
          _type: "block";
          _key: string;
        }> | null;
      }
  > | null;
  seoTitle?: string;
  seoDescription?: string;
  seoImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  seoNoIndex?: boolean;
  ogTitle?: string;
  ogDescription?: string;
} | null;
// Variable: querySlugPagePaths
// Query: *[_type == "page" && defined(slug.current)].slug.current
export type QuerySlugPagePathsResult = Array<string>;
// Variable: queryBlogIndexPageData
// Query: *[_type == "blogIndex"][0]{    ...,    _id,    _type,    title,    description,    "displayFeaturedBlogs" : displayFeaturedBlogs == "yes",    "featuredBlogsCount" : featuredBlogsCount,      pageBuilder[]{    ...,    _type,      _type == "cta" => {    ...,      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  },      buttons[]{    text,    variant,    _key,    _type,    "openInNewTab": url.openInNewTab,    "href": select(      url.type == "internal" => url.internal->slug.current,      url.type == "external" => url.external,      url.href    ),  },  },      _type == "hero" => {    ...,      image{    ...,    ...asset->{      "alt": coalesce(altText, originalFilename, "no-alt"),      "blurData": metadata.lqip,      "dominantColor": metadata.palette.dominant.background    },  },      buttons[]{    text,    variant,    _key,    _type,    "openInNewTab": url.openInNewTab,    "href": select(      url.type == "internal" => url.internal->slug.current,      url.type == "external" => url.external,      url.href    ),  },      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  }  },      _type == "faqAccordion" => {    ...,      "faqs": array::compact(faqs[]->{    title,    _id,    _type,      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  }  }),    link{      ...,      "openInNewTab": url.openInNewTab,      "href": select(        url.type == "internal" => url.internal->slug.current,        url.type == "external" => url.external,        url.href      )    }  },      _type == "featureCardsIcon" => {    ...,      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  },    "cards": array::compact(cards[]{      ...,        richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  },    })  },      _type == "subscribeNewsletter" => {    ...,    "subTitle": subTitle[]{      ...,        markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }    },    "helperText": helperText[]{      ...,        markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }    }  },      _type == "imageLinkCards" => {    ...,      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  },      buttons[]{    text,    variant,    _key,    _type,    "openInNewTab": url.openInNewTab,    "href": select(      url.type == "internal" => url.internal->slug.current,      url.type == "external" => url.external,      url.href    ),  },    "cards": array::compact(cards[]{      ...,      "openInNewTab": url.openInNewTab,      "href": select(        url.type == "internal" => url.internal->slug.current,        url.type == "external" => url.external,        url.href      ),        image{    ...,    ...asset->{      "alt": coalesce(altText, originalFilename, "no-alt"),      "blurData": metadata.lqip,      "dominantColor": metadata.palette.dominant.background    },  },    })  }  },    "slug": slug.current,    "blogs": *[_type == "blog" && (seoHideFromLists != true)] | order(orderRank asc){        _type,  _id,  title,  description,  "slug":slug.current,  richText,  orderRank,    image{    ...,    ...asset->{      "alt": coalesce(altText, originalFilename, "no-alt"),      "blurData": metadata.lqip,      "dominantColor": metadata.palette.dominant.background    },  },  publishedAt,    authors[0]->{    _id,    name,    position,      image{    ...,    ...asset->{      "alt": coalesce(altText, originalFilename, "no-alt"),      "blurData": metadata.lqip,      "dominantColor": metadata.palette.dominant.background    },  }  }    }  }
export type QueryBlogIndexPageDataResult = {
  _id: string;
  _type: "blogIndex";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title: string | null;
  description: string | null;
  slug: string;
  displayFeaturedBlogs: false | true;
  featuredBlogsCount: "1" | "2" | "3" | null;
  pageBuilder: Array<
    | {
        _key: string;
        _type: "cta";
        eyebrow?: string;
        title?: string;
        richText: Array<
          | {
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: "span";
                _key: string;
              }>;
              style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
              listItem?: "bullet" | "number";
              markDefs: Array<
                | {
                    customLink?: CustomUrl;
                    _type: "customLink";
                    _key: string;
                    openInNewTab: boolean | null;
                    href: string | "#" | null;
                  }
                | {
                    customLink?: CustomUrl;
                    _type: "customLink";
                    _key: string;
                  }
              > | null;
              level?: number;
              _type: "block";
              _key: string;
            }
          | {
              asset?: {
                _ref: string;
                _type: "reference";
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              caption?: string;
              _type: "image";
              _key: string;
              markDefs: null;
            }
        > | null;
        buttons: Array<{
          text: string | null;
          variant: "default" | "link" | "outline" | "secondary" | null;
          _key: string;
          _type: "button";
          openInNewTab: boolean | null;
          href: string | null;
        }> | null;
      }
    | {
        _key: string;
        _type: "faqAccordion";
        eyebrow?: string;
        title: string;
        subtitle?: string;
        link: {
          title?: string;
          description?: string;
          url?: CustomUrl;
          openInNewTab: boolean | null;
          href: string | null;
        } | null;
        faqs: Array<{
          title: string;
          _id: string;
          _type: "faq";
          richText: Array<
            | {
                children?: Array<{
                  marks?: Array<string>;
                  text?: string;
                  _type: "span";
                  _key: string;
                }>;
                style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
                listItem?: "bullet" | "number";
                markDefs: Array<
                  | {
                      customLink?: CustomUrl;
                      _type: "customLink";
                      _key: string;
                      openInNewTab: boolean | null;
                      href: string | "#" | null;
                    }
                  | {
                      customLink?: CustomUrl;
                      _type: "customLink";
                      _key: string;
                    }
                > | null;
                level?: number;
                _type: "block";
                _key: string;
              }
            | {
                asset?: {
                  _ref: string;
                  _type: "reference";
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                caption?: string;
                _type: "image";
                _key: string;
                markDefs: null;
              }
          > | null;
        }>;
      }
    | {
        _key: string;
        _type: "featureCardsIcon";
        eyebrow?: string;
        title?: string;
        richText: Array<{
          children?: Array<{
            marks?: Array<string>;
            text?: string;
            _type: "span";
            _key: string;
          }>;
          style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
          listItem?: "bullet" | "number";
          markDefs: Array<
            | {
                customLink?: CustomUrl;
                _type: "customLink";
                _key: string;
                openInNewTab: boolean | null;
                href: string | "#" | null;
              }
            | {
                customLink?: CustomUrl;
                _type: "customLink";
                _key: string;
              }
          > | null;
          level?: number;
          _type: "block";
          _key: string;
        }> | null;
        cards: Array<{
          icon?: IconPicker;
          title?: string;
          richText: Array<{
            children?: Array<{
              marks?: Array<string>;
              text?: string;
              _type: "span";
              _key: string;
            }>;
            style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
            listItem?: "bullet" | "number";
            markDefs: Array<
              | {
                  customLink?: CustomUrl;
                  _type: "customLink";
                  _key: string;
                  openInNewTab: boolean | null;
                  href: string | "#" | null;
                }
              | {
                  customLink?: CustomUrl;
                  _type: "customLink";
                  _key: string;
                }
            > | null;
            level?: number;
            _type: "block";
            _key: string;
          }> | null;
          _type: "featureCardIcon";
          _key: string;
        }> | null;
      }
    | {
        _key: string;
        _type: "hero";
        badge?: string;
        title?: string;
        richText: Array<
          | {
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: "span";
                _key: string;
              }>;
              style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
              listItem?: "bullet" | "number";
              markDefs: Array<
                | {
                    customLink?: CustomUrl;
                    _type: "customLink";
                    _key: string;
                    openInNewTab: boolean | null;
                    href: string | "#" | null;
                  }
                | {
                    customLink?: CustomUrl;
                    _type: "customLink";
                    _key: string;
                  }
              > | null;
              level?: number;
              _type: "block";
              _key: string;
            }
          | {
              asset?: {
                _ref: string;
                _type: "reference";
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              caption?: string;
              _type: "image";
              _key: string;
              markDefs: null;
            }
        > | null;
        image:
          | {
              asset?: {
                _ref: string;
                _type: "reference";
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: "image";
              alt: string | "no-alt";
              blurData: string | null;
              dominantColor: string | null;
            }
          | {
              asset?: {
                _ref: string;
                _type: "reference";
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: "image";
            }
          | null;
        buttons: Array<{
          text: string | null;
          variant: "default" | "link" | "outline" | "secondary" | null;
          _key: string;
          _type: "button";
          openInNewTab: boolean | null;
          href: string | null;
        }> | null;
      }
    | {
        _key: string;
        _type: "imageLinkCards";
        eyebrow?: string;
        title: string;
        richText: Array<
          | {
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: "span";
                _key: string;
              }>;
              style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
              listItem?: "bullet" | "number";
              markDefs: Array<
                | {
                    customLink?: CustomUrl;
                    _type: "customLink";
                    _key: string;
                    openInNewTab: boolean | null;
                    href: string | "#" | null;
                  }
                | {
                    customLink?: CustomUrl;
                    _type: "customLink";
                    _key: string;
                  }
              > | null;
              level?: number;
              _type: "block";
              _key: string;
            }
          | {
              asset?: {
                _ref: string;
                _type: "reference";
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              caption?: string;
              _type: "image";
              _key: string;
              markDefs: null;
            }
        > | null;
        buttons: Array<{
          text: string | null;
          variant: "default" | "link" | "outline" | "secondary" | null;
          _key: string;
          _type: "button";
          openInNewTab: boolean | null;
          href: string | null;
        }> | null;
        cards: Array<{
          title: string;
          description: string;
          image:
            | {
                asset?: {
                  _ref: string;
                  _type: "reference";
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: "image";
                alt: string | "no-alt";
                blurData: string | null;
                dominantColor: string | null;
              }
            | {
                asset?: {
                  _ref: string;
                  _type: "reference";
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: "image";
              }
            | null;
          url?: CustomUrl;
          _type: "imageLinkCard";
          _key: string;
          openInNewTab: boolean | null;
          href: string | null;
        }> | null;
      }
    | {
        _key: string;
        _type: "subscribeNewsletter";
        title?: string;
        subTitle: Array<{
          children?: Array<{
            marks?: Array<string>;
            text?: string;
            _type: "span";
            _key: string;
          }>;
          style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
          listItem?: "bullet" | "number";
          markDefs: Array<
            | {
                customLink?: CustomUrl;
                _type: "customLink";
                _key: string;
                openInNewTab: boolean | null;
                href: string | "#" | null;
              }
            | {
                customLink?: CustomUrl;
                _type: "customLink";
                _key: string;
              }
          > | null;
          level?: number;
          _type: "block";
          _key: string;
        }> | null;
        helperText: Array<{
          children?: Array<{
            marks?: Array<string>;
            text?: string;
            _type: "span";
            _key: string;
          }>;
          style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
          listItem?: "bullet" | "number";
          markDefs: Array<
            | {
                customLink?: CustomUrl;
                _type: "customLink";
                _key: string;
                openInNewTab: boolean | null;
                href: string | "#" | null;
              }
            | {
                customLink?: CustomUrl;
                _type: "customLink";
                _key: string;
              }
          > | null;
          level?: number;
          _type: "block";
          _key: string;
        }> | null;
      }
  > | null;
  seoTitle?: string;
  seoDescription?: string;
  seoImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  ogTitle?: string;
  ogDescription?: string;
  blogs: Array<{
    _type: "blog";
    _id: string;
    title: string;
    description: string | null;
    slug: string;
    richText: RichText | null;
    orderRank: string | null;
    image:
      | {
          asset?: {
            _ref: string;
            _type: "reference";
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: "image";
          alt: string | "no-alt";
          blurData: string | null;
          dominantColor: string | null;
        }
      | {
          asset?: {
            _ref: string;
            _type: "reference";
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: "image";
        };
    publishedAt: string | null;
    authors: {
      _id: string;
      name: string;
      position: string | null;
      image:
        | {
            asset?: {
              _ref: string;
              _type: "reference";
              _weak?: boolean;
              [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
            };
            media?: unknown;
            hotspot?: SanityImageHotspot;
            crop?: SanityImageCrop;
            _type: "image";
            alt: string | "no-alt";
            blurData: string | null;
            dominantColor: string | null;
          }
        | {
            asset?: {
              _ref: string;
              _type: "reference";
              _weak?: boolean;
              [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
            };
            media?: unknown;
            hotspot?: SanityImageHotspot;
            crop?: SanityImageCrop;
            _type: "image";
          }
        | null;
    } | null;
  }>;
} | null;
// Variable: queryBlogSlugPageData
// Query: *[_type == "blog" && slug.current == $slug][0]{    ...,    "slug": slug.current,      authors[0]->{    _id,    name,    position,      image{    ...,    ...asset->{      "alt": coalesce(altText, originalFilename, "no-alt"),      "blurData": metadata.lqip,      "dominantColor": metadata.palette.dominant.background    },  }  },      image{    ...,    ...asset->{      "alt": coalesce(altText, originalFilename, "no-alt"),      "blurData": metadata.lqip,      "dominantColor": metadata.palette.dominant.background    },  },      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  },      pageBuilder[]{    ...,    _type,      _type == "cta" => {    ...,      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  },      buttons[]{    text,    variant,    _key,    _type,    "openInNewTab": url.openInNewTab,    "href": select(      url.type == "internal" => url.internal->slug.current,      url.type == "external" => url.external,      url.href    ),  },  },      _type == "hero" => {    ...,      image{    ...,    ...asset->{      "alt": coalesce(altText, originalFilename, "no-alt"),      "blurData": metadata.lqip,      "dominantColor": metadata.palette.dominant.background    },  },      buttons[]{    text,    variant,    _key,    _type,    "openInNewTab": url.openInNewTab,    "href": select(      url.type == "internal" => url.internal->slug.current,      url.type == "external" => url.external,      url.href    ),  },      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  }  },      _type == "faqAccordion" => {    ...,      "faqs": array::compact(faqs[]->{    title,    _id,    _type,      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  }  }),    link{      ...,      "openInNewTab": url.openInNewTab,      "href": select(        url.type == "internal" => url.internal->slug.current,        url.type == "external" => url.external,        url.href      )    }  },      _type == "featureCardsIcon" => {    ...,      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  },    "cards": array::compact(cards[]{      ...,        richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  },    })  },      _type == "subscribeNewsletter" => {    ...,    "subTitle": subTitle[]{      ...,        markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }    },    "helperText": helperText[]{      ...,        markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }    }  },      _type == "imageLinkCards" => {    ...,      richText[]{    ...,      markDefs[]{    ...,      ...customLink{    openInNewTab,    "href": select(      type == "internal" => internal->slug.current,      type == "external" => external,      "#"    ),  }  }  },      buttons[]{    text,    variant,    _key,    _type,    "openInNewTab": url.openInNewTab,    "href": select(      url.type == "internal" => url.internal->slug.current,      url.type == "external" => url.external,      url.href    ),  },    "cards": array::compact(cards[]{      ...,      "openInNewTab": url.openInNewTab,      "href": select(        url.type == "internal" => url.internal->slug.current,        url.type == "external" => url.external,        url.href      ),        image{    ...,    ...asset->{      "alt": coalesce(altText, originalFilename, "no-alt"),      "blurData": metadata.lqip,      "dominantColor": metadata.palette.dominant.background    },  },    })  }  }  }
export type QueryBlogSlugPageDataResult = {
  _id: string;
  _type: "blog";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  orderRank?: string;
  title: string;
  description?: string;
  slug: string;
  authors: {
    _id: string;
    name: string;
    position: string | null;
    image:
      | {
          asset?: {
            _ref: string;
            _type: "reference";
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: "image";
          alt: string | "no-alt";
          blurData: string | null;
          dominantColor: string | null;
        }
      | {
          asset?: {
            _ref: string;
            _type: "reference";
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: "image";
        }
      | null;
  } | null;
  publishedAt?: string;
  image:
    | {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        _type: "image";
        alt: string | "no-alt";
        blurData: string | null;
        dominantColor: string | null;
      }
    | {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        _type: "image";
      };
  richText: Array<
    | {
        children?: Array<{
          marks?: Array<string>;
          text?: string;
          _type: "span";
          _key: string;
        }>;
        style?: "h2" | "h3" | "h4" | "h5" | "h6" | "inline" | "normal";
        listItem?: "bullet" | "number";
        markDefs: Array<
          | {
              customLink?: CustomUrl;
              _type: "customLink";
              _key: string;
              openInNewTab: boolean | null;
              href: string | "#" | null;
            }
          | {
              customLink?: CustomUrl;
              _type: "customLink";
              _key: string;
            }
        > | null;
        level?: number;
        _type: "block";
        _key: string;
      }
    | {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        caption?: string;
        _type: "image";
        _key: string;
        markDefs: null;
      }
  > | null;
  seoTitle?: string;
  seoDescription?: string;
  seoImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  seoNoIndex?: boolean;
  seoHideFromLists?: boolean;
  ogTitle?: string;
  ogDescription?: string;
  pageBuilder: null;
} | null;
// Variable: queryBlogPaths
// Query: *[_type == "blog" && defined(slug.current)].slug.current
export type QueryBlogPathsResult = Array<string>;
// Variable: queryHomePageOGData
// Query: *[_type == "homePage" && _id == $id][0]{      _id,  _type,  "title": select(    defined(ogTitle) => ogTitle,    defined(seoTitle) => seoTitle,    title  ),  "description": select(    defined(ogDescription) => ogDescription,    defined(seoDescription) => seoDescription,    description  ),  "image": image.asset->url + "?w=566&h=566&dpr=2&fit=max",  "dominantColor": image.asset->metadata.palette.dominant.background,  "seoImage": seoImage.asset->url + "?w=1200&h=630&dpr=2&fit=max",   "logo": *[_type == "settings"][0].logo.asset->url + "?w=80&h=40&dpr=3&fit=max&q=100",  "date": coalesce(date, _createdAt)  }
export type QueryHomePageOGDataResult = {
  _id: string;
  _type: "homePage";
  title: string | null;
  description: string | null;
  image: null;
  dominantColor: null;
  seoImage: string | null;
  logo: string | null;
  date: string;
} | null;
// Variable: querySlugPageOGData
// Query: *[_type == "page" && _id == $id][0]{      _id,  _type,  "title": select(    defined(ogTitle) => ogTitle,    defined(seoTitle) => seoTitle,    title  ),  "description": select(    defined(ogDescription) => ogDescription,    defined(seoDescription) => seoDescription,    description  ),  "image": image.asset->url + "?w=566&h=566&dpr=2&fit=max",  "dominantColor": image.asset->metadata.palette.dominant.background,  "seoImage": seoImage.asset->url + "?w=1200&h=630&dpr=2&fit=max",   "logo": *[_type == "settings"][0].logo.asset->url + "?w=80&h=40&dpr=3&fit=max&q=100",  "date": coalesce(date, _createdAt)  }
export type QuerySlugPageOGDataResult = {
  _id: string;
  _type: "page";
  title: string | null;
  description: string | null;
  image: string | null;
  dominantColor: string | null;
  seoImage: string | null;
  logo: string | null;
  date: string;
} | null;
// Variable: queryBlogPageOGData
// Query: *[_type == "blog" && _id == $id][0]{      _id,  _type,  "title": select(    defined(ogTitle) => ogTitle,    defined(seoTitle) => seoTitle,    title  ),  "description": select(    defined(ogDescription) => ogDescription,    defined(seoDescription) => seoDescription,    description  ),  "image": image.asset->url + "?w=566&h=566&dpr=2&fit=max",  "dominantColor": image.asset->metadata.palette.dominant.background,  "seoImage": seoImage.asset->url + "?w=1200&h=630&dpr=2&fit=max",   "logo": *[_type == "settings"][0].logo.asset->url + "?w=80&h=40&dpr=3&fit=max&q=100",  "date": coalesce(date, _createdAt)  }
export type QueryBlogPageOGDataResult = {
  _id: string;
  _type: "blog";
  title: string | null;
  description: string | null;
  image: string | null;
  dominantColor: string | null;
  seoImage: string | null;
  logo: string | null;
  date: string;
} | null;
// Variable: queryGenericPageOGData
// Query: *[ defined(slug.current) && _id == $id][0]{      _id,  _type,  "title": select(    defined(ogTitle) => ogTitle,    defined(seoTitle) => seoTitle,    title  ),  "description": select(    defined(ogDescription) => ogDescription,    defined(seoDescription) => seoDescription,    description  ),  "image": image.asset->url + "?w=566&h=566&dpr=2&fit=max",  "dominantColor": image.asset->metadata.palette.dominant.background,  "seoImage": seoImage.asset->url + "?w=1200&h=630&dpr=2&fit=max",   "logo": *[_type == "settings"][0].logo.asset->url + "?w=80&h=40&dpr=3&fit=max&q=100",  "date": coalesce(date, _createdAt)  }
export type QueryGenericPageOGDataResult =
  | {
      _id: string;
      _type: "blog";
      title: string | null;
      description: string | null;
      image: string | null;
      dominantColor: string | null;
      seoImage: string | null;
      logo: string | null;
      date: string;
    }
  | {
      _id: string;
      _type: "blogIndex";
      title: string | null;
      description: string | null;
      image: null;
      dominantColor: null;
      seoImage: string | null;
      logo: string | null;
      date: string;
    }
  | {
      _id: string;
      _type: "homePage";
      title: string | null;
      description: string | null;
      image: null;
      dominantColor: null;
      seoImage: string | null;
      logo: string | null;
      date: string;
    }
  | {
      _id: string;
      _type: "page";
      title: string | null;
      description: string | null;
      image: string | null;
      dominantColor: string | null;
      seoImage: string | null;
      logo: string | null;
      date: string;
    }
  | null;
// Variable: queryFooterData
// Query: *[_type == "footer" && _id == "footer"][0]{    _id,    subtitle,    columns[]{      _key,      title,      links[]{        _key,        name,        "openInNewTab": url.openInNewTab,        "href": select(          url.type == "internal" => url.internal->slug.current,          url.type == "external" => url.external,          url.href        ),      }    }  }
export type QueryFooterDataResult = {
  _id: string;
  subtitle: string | null;
  columns: Array<{
    _key: string;
    title: string | null;
    links: Array<{
      _key: string;
      name: string | null;
      openInNewTab: boolean | null;
      href: string | null;
    }> | null;
  }> | null;
} | null;
// Variable: queryNavbarData
// Query: *[_type == "navbar" && _id == "navbar"][0]{    _id,    columns[]{      _key,      _type == "navbarColumn" => {        "type": "column",        title,        links[]{          _key,          name,          icon,          description,          "openInNewTab": url.openInNewTab,          "href": select(            url.type == "internal" => url.internal->slug.current,            url.type == "external" => url.external,            url.href          )        }      },      _type == "navbarLink" => {        "type": "link",        name,        description,        "openInNewTab": url.openInNewTab,        "href": select(          url.type == "internal" => url.internal->slug.current,          url.type == "external" => url.external,          url.href        )      }    },      buttons[]{    text,    variant,    _key,    _type,    "openInNewTab": url.openInNewTab,    "href": select(      url.type == "internal" => url.internal->slug.current,      url.type == "external" => url.external,      url.href    ),  },  }
export type QueryNavbarDataResult = {
  _id: string;
  columns: Array<
    | {
        _key: string;
        type: "link";
        name: string | null;
        description: null;
        openInNewTab: boolean | null;
        href: string | null;
      }
    | {
        _key: string;
        type: "column";
        title: string | null;
        links: Array<{
          _key: string;
          name: string | null;
          icon: IconPicker | null;
          description: string | null;
          openInNewTab: boolean | null;
          href: string | null;
        }>;
      }
  > | null;
  buttons: Array<{
    text: string | null;
    variant: "default" | "link" | "outline" | "secondary" | null;
    _key: string;
    _type: "button";
    openInNewTab: boolean | null;
    href: string | null;
  }> | null;
} | null;
// Variable: querySitemapData
// Query: {  "slugPages": *[_type == "page" && defined(slug.current)]{    "slug": slug.current,    "lastModified": _updatedAt  },  "blogPages": *[_type == "blog" && defined(slug.current)]{    "slug": slug.current,    "lastModified": _updatedAt  }}
export type QuerySitemapDataResult = {
  slugPages: Array<{
    slug: string;
    lastModified: string;
  }>;
  blogPages: Array<{
    slug: string;
    lastModified: string;
  }>;
};
// Variable: queryGlobalSeoSettings
// Query: *[_type == "settings"][0]{    _id,    _type,    siteTitle,    logo{      ...,      ...asset->{        "alt": coalesce(altText, originalFilename, "no-alt"),        "blurData": metadata.lqip,        "dominantColor": metadata.palette.dominant.background      }    },    siteDescription,    socialLinks{      linkedin,      facebook,      twitter,      instagram,      youtube    }  }
export type QueryGlobalSeoSettingsResult = {
  _id: string;
  _type: "settings";
  siteTitle: string;
  logo:
    | {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        _type: "image";
        alt: string | "no-alt";
        blurData: string | null;
        dominantColor: string | null;
      }
    | {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        _type: "image";
      }
    | null;
  siteDescription: string;
  socialLinks: {
    linkedin: string | null;
    facebook: string | null;
    twitter: string | null;
    instagram: string | null;
    youtube: string | null;
  } | null;
} | null;
// Variable: querySettingsData
// Query: *[_type == "settings"][0]{    _id,    _type,    siteTitle,    siteDescription,    "logo": logo.asset->url + "?w=80&h=40&dpr=3&fit=max",    "socialLinks": socialLinks,    "contactEmail": contactEmail,  }
export type QuerySettingsDataResult = {
  _id: string;
  _type: "settings";
  siteTitle: string;
  siteDescription: string;
  logo: string | null;
  socialLinks: {
    linkedin?: string;
    facebook?: string;
    twitter?: string;
    instagram?: string;
    youtube?: string;
  } | null;
  contactEmail: string | null;
} | null;

// Query TypeMap
import "@sanity/client";
declare module "@sanity/client" {
  interface SanityQueries {
    '\n  *[_type == "page" && defined(image)][0]{\n    \n  image{\n    ...,\n    ...asset->{\n      "alt": coalesce(altText, originalFilename, "no-alt"),\n      "blurData": metadata.lqip,\n      "dominantColor": metadata.palette.dominant.background\n    },\n  }\n\n  }.image\n': QueryImageTypeResult;
    '*[_type == "homePage" && _id == "homePage"][0]{\n    ...,\n    _id,\n    _type,\n    "slug": slug.current,\n    title,\n    description,\n    \n  pageBuilder[]{\n    ...,\n    _type,\n    \n  _type == "cta" => {\n    ...,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n,\n    \n  buttons[]{\n    text,\n    variant,\n    _key,\n    _type,\n    "openInNewTab": url.openInNewTab,\n    "href": select(\n      url.type == "internal" => url.internal->slug.current,\n      url.type == "external" => url.external,\n      url.href\n    ),\n  }\n,\n  }\n,\n    \n  _type == "hero" => {\n    ...,\n    \n  image{\n    ...,\n    ...asset->{\n      "alt": coalesce(altText, originalFilename, "no-alt"),\n      "blurData": metadata.lqip,\n      "dominantColor": metadata.palette.dominant.background\n    },\n  }\n,\n    \n  buttons[]{\n    text,\n    variant,\n    _key,\n    _type,\n    "openInNewTab": url.openInNewTab,\n    "href": select(\n      url.type == "internal" => url.internal->slug.current,\n      url.type == "external" => url.external,\n      url.href\n    ),\n  }\n,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n\n  }\n,\n    \n  _type == "faqAccordion" => {\n    ...,\n    \n  "faqs": array::compact(faqs[]->{\n    title,\n    _id,\n    _type,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n\n  })\n,\n    link{\n      ...,\n      "openInNewTab": url.openInNewTab,\n      "href": select(\n        url.type == "internal" => url.internal->slug.current,\n        url.type == "external" => url.external,\n        url.href\n      )\n    }\n  }\n,\n    \n  _type == "featureCardsIcon" => {\n    ...,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n,\n    "cards": array::compact(cards[]{\n      ...,\n      \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n,\n    })\n  }\n,\n    \n  _type == "subscribeNewsletter" => {\n    ...,\n    "subTitle": subTitle[]{\n      ...,\n      \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n    },\n    "helperText": helperText[]{\n      ...,\n      \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n    }\n  }\n,\n    \n  _type == "imageLinkCards" => {\n    ...,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n,\n    \n  buttons[]{\n    text,\n    variant,\n    _key,\n    _type,\n    "openInNewTab": url.openInNewTab,\n    "href": select(\n      url.type == "internal" => url.internal->slug.current,\n      url.type == "external" => url.external,\n      url.href\n    ),\n  }\n,\n    "cards": array::compact(cards[]{\n      ...,\n      "openInNewTab": url.openInNewTab,\n      "href": select(\n        url.type == "internal" => url.internal->slug.current,\n        url.type == "external" => url.external,\n        url.href\n      ),\n      \n  image{\n    ...,\n    ...asset->{\n      "alt": coalesce(altText, originalFilename, "no-alt"),\n      "blurData": metadata.lqip,\n      "dominantColor": metadata.palette.dominant.background\n    },\n  }\n,\n    })\n  }\n\n  }\n\n  }': QueryHomePageDataResult;
    '\n  *[_type == "page" && slug.current == $slug][0]{\n    ...,\n    "slug": slug.current,\n    \n  pageBuilder[]{\n    ...,\n    _type,\n    \n  _type == "cta" => {\n    ...,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n,\n    \n  buttons[]{\n    text,\n    variant,\n    _key,\n    _type,\n    "openInNewTab": url.openInNewTab,\n    "href": select(\n      url.type == "internal" => url.internal->slug.current,\n      url.type == "external" => url.external,\n      url.href\n    ),\n  }\n,\n  }\n,\n    \n  _type == "hero" => {\n    ...,\n    \n  image{\n    ...,\n    ...asset->{\n      "alt": coalesce(altText, originalFilename, "no-alt"),\n      "blurData": metadata.lqip,\n      "dominantColor": metadata.palette.dominant.background\n    },\n  }\n,\n    \n  buttons[]{\n    text,\n    variant,\n    _key,\n    _type,\n    "openInNewTab": url.openInNewTab,\n    "href": select(\n      url.type == "internal" => url.internal->slug.current,\n      url.type == "external" => url.external,\n      url.href\n    ),\n  }\n,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n\n  }\n,\n    \n  _type == "faqAccordion" => {\n    ...,\n    \n  "faqs": array::compact(faqs[]->{\n    title,\n    _id,\n    _type,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n\n  })\n,\n    link{\n      ...,\n      "openInNewTab": url.openInNewTab,\n      "href": select(\n        url.type == "internal" => url.internal->slug.current,\n        url.type == "external" => url.external,\n        url.href\n      )\n    }\n  }\n,\n    \n  _type == "featureCardsIcon" => {\n    ...,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n,\n    "cards": array::compact(cards[]{\n      ...,\n      \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n,\n    })\n  }\n,\n    \n  _type == "subscribeNewsletter" => {\n    ...,\n    "subTitle": subTitle[]{\n      ...,\n      \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n    },\n    "helperText": helperText[]{\n      ...,\n      \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n    }\n  }\n,\n    \n  _type == "imageLinkCards" => {\n    ...,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n,\n    \n  buttons[]{\n    text,\n    variant,\n    _key,\n    _type,\n    "openInNewTab": url.openInNewTab,\n    "href": select(\n      url.type == "internal" => url.internal->slug.current,\n      url.type == "external" => url.external,\n      url.href\n    ),\n  }\n,\n    "cards": array::compact(cards[]{\n      ...,\n      "openInNewTab": url.openInNewTab,\n      "href": select(\n        url.type == "internal" => url.internal->slug.current,\n        url.type == "external" => url.external,\n        url.href\n      ),\n      \n  image{\n    ...,\n    ...asset->{\n      "alt": coalesce(altText, originalFilename, "no-alt"),\n      "blurData": metadata.lqip,\n      "dominantColor": metadata.palette.dominant.background\n    },\n  }\n,\n    })\n  }\n\n  }\n\n  }\n  ': QuerySlugPageDataResult;
    '\n  *[_type == "page" && defined(slug.current)].slug.current\n': QuerySlugPagePathsResult;
    '\n  *[_type == "blogIndex"][0]{\n    ...,\n    _id,\n    _type,\n    title,\n    description,\n    "displayFeaturedBlogs" : displayFeaturedBlogs == "yes",\n    "featuredBlogsCount" : featuredBlogsCount,\n    \n  pageBuilder[]{\n    ...,\n    _type,\n    \n  _type == "cta" => {\n    ...,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n,\n    \n  buttons[]{\n    text,\n    variant,\n    _key,\n    _type,\n    "openInNewTab": url.openInNewTab,\n    "href": select(\n      url.type == "internal" => url.internal->slug.current,\n      url.type == "external" => url.external,\n      url.href\n    ),\n  }\n,\n  }\n,\n    \n  _type == "hero" => {\n    ...,\n    \n  image{\n    ...,\n    ...asset->{\n      "alt": coalesce(altText, originalFilename, "no-alt"),\n      "blurData": metadata.lqip,\n      "dominantColor": metadata.palette.dominant.background\n    },\n  }\n,\n    \n  buttons[]{\n    text,\n    variant,\n    _key,\n    _type,\n    "openInNewTab": url.openInNewTab,\n    "href": select(\n      url.type == "internal" => url.internal->slug.current,\n      url.type == "external" => url.external,\n      url.href\n    ),\n  }\n,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n\n  }\n,\n    \n  _type == "faqAccordion" => {\n    ...,\n    \n  "faqs": array::compact(faqs[]->{\n    title,\n    _id,\n    _type,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n\n  })\n,\n    link{\n      ...,\n      "openInNewTab": url.openInNewTab,\n      "href": select(\n        url.type == "internal" => url.internal->slug.current,\n        url.type == "external" => url.external,\n        url.href\n      )\n    }\n  }\n,\n    \n  _type == "featureCardsIcon" => {\n    ...,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n,\n    "cards": array::compact(cards[]{\n      ...,\n      \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n,\n    })\n  }\n,\n    \n  _type == "subscribeNewsletter" => {\n    ...,\n    "subTitle": subTitle[]{\n      ...,\n      \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n    },\n    "helperText": helperText[]{\n      ...,\n      \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n    }\n  }\n,\n    \n  _type == "imageLinkCards" => {\n    ...,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n,\n    \n  buttons[]{\n    text,\n    variant,\n    _key,\n    _type,\n    "openInNewTab": url.openInNewTab,\n    "href": select(\n      url.type == "internal" => url.internal->slug.current,\n      url.type == "external" => url.external,\n      url.href\n    ),\n  }\n,\n    "cards": array::compact(cards[]{\n      ...,\n      "openInNewTab": url.openInNewTab,\n      "href": select(\n        url.type == "internal" => url.internal->slug.current,\n        url.type == "external" => url.external,\n        url.href\n      ),\n      \n  image{\n    ...,\n    ...asset->{\n      "alt": coalesce(altText, originalFilename, "no-alt"),\n      "blurData": metadata.lqip,\n      "dominantColor": metadata.palette.dominant.background\n    },\n  }\n,\n    })\n  }\n\n  }\n,\n    "slug": slug.current,\n    "blogs": *[_type == "blog" && (seoHideFromLists != true)] | order(orderRank asc){\n      \n  _type,\n  _id,\n  title,\n  description,\n  "slug":slug.current,\n  richText,\n  orderRank,\n  \n  image{\n    ...,\n    ...asset->{\n      "alt": coalesce(altText, originalFilename, "no-alt"),\n      "blurData": metadata.lqip,\n      "dominantColor": metadata.palette.dominant.background\n    },\n  }\n,\n  publishedAt,\n  \n  authors[0]->{\n    _id,\n    name,\n    position,\n    \n  image{\n    ...,\n    ...asset->{\n      "alt": coalesce(altText, originalFilename, "no-alt"),\n      "blurData": metadata.lqip,\n      "dominantColor": metadata.palette.dominant.background\n    },\n  }\n\n  }\n\n\n    }\n  }\n': QueryBlogIndexPageDataResult;
    '\n  *[_type == "blog" && slug.current == $slug][0]{\n    ...,\n    "slug": slug.current,\n    \n  authors[0]->{\n    _id,\n    name,\n    position,\n    \n  image{\n    ...,\n    ...asset->{\n      "alt": coalesce(altText, originalFilename, "no-alt"),\n      "blurData": metadata.lqip,\n      "dominantColor": metadata.palette.dominant.background\n    },\n  }\n\n  }\n,\n    \n  image{\n    ...,\n    ...asset->{\n      "alt": coalesce(altText, originalFilename, "no-alt"),\n      "blurData": metadata.lqip,\n      "dominantColor": metadata.palette.dominant.background\n    },\n  }\n,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n,\n    \n  pageBuilder[]{\n    ...,\n    _type,\n    \n  _type == "cta" => {\n    ...,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n,\n    \n  buttons[]{\n    text,\n    variant,\n    _key,\n    _type,\n    "openInNewTab": url.openInNewTab,\n    "href": select(\n      url.type == "internal" => url.internal->slug.current,\n      url.type == "external" => url.external,\n      url.href\n    ),\n  }\n,\n  }\n,\n    \n  _type == "hero" => {\n    ...,\n    \n  image{\n    ...,\n    ...asset->{\n      "alt": coalesce(altText, originalFilename, "no-alt"),\n      "blurData": metadata.lqip,\n      "dominantColor": metadata.palette.dominant.background\n    },\n  }\n,\n    \n  buttons[]{\n    text,\n    variant,\n    _key,\n    _type,\n    "openInNewTab": url.openInNewTab,\n    "href": select(\n      url.type == "internal" => url.internal->slug.current,\n      url.type == "external" => url.external,\n      url.href\n    ),\n  }\n,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n\n  }\n,\n    \n  _type == "faqAccordion" => {\n    ...,\n    \n  "faqs": array::compact(faqs[]->{\n    title,\n    _id,\n    _type,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n\n  })\n,\n    link{\n      ...,\n      "openInNewTab": url.openInNewTab,\n      "href": select(\n        url.type == "internal" => url.internal->slug.current,\n        url.type == "external" => url.external,\n        url.href\n      )\n    }\n  }\n,\n    \n  _type == "featureCardsIcon" => {\n    ...,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n,\n    "cards": array::compact(cards[]{\n      ...,\n      \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n,\n    })\n  }\n,\n    \n  _type == "subscribeNewsletter" => {\n    ...,\n    "subTitle": subTitle[]{\n      ...,\n      \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n    },\n    "helperText": helperText[]{\n      ...,\n      \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n    }\n  }\n,\n    \n  _type == "imageLinkCards" => {\n    ...,\n    \n  richText[]{\n    ...,\n    \n  markDefs[]{\n    ...,\n    \n  ...customLink{\n    openInNewTab,\n    "href": select(\n      type == "internal" => internal->slug.current,\n      type == "external" => external,\n      "#"\n    ),\n  }\n\n  }\n\n  }\n,\n    \n  buttons[]{\n    text,\n    variant,\n    _key,\n    _type,\n    "openInNewTab": url.openInNewTab,\n    "href": select(\n      url.type == "internal" => url.internal->slug.current,\n      url.type == "external" => url.external,\n      url.href\n    ),\n  }\n,\n    "cards": array::compact(cards[]{\n      ...,\n      "openInNewTab": url.openInNewTab,\n      "href": select(\n        url.type == "internal" => url.internal->slug.current,\n        url.type == "external" => url.external,\n        url.href\n      ),\n      \n  image{\n    ...,\n    ...asset->{\n      "alt": coalesce(altText, originalFilename, "no-alt"),\n      "blurData": metadata.lqip,\n      "dominantColor": metadata.palette.dominant.background\n    },\n  }\n,\n    })\n  }\n\n  }\n\n  }\n': QueryBlogSlugPageDataResult;
    '\n  *[_type == "blog" && defined(slug.current)].slug.current\n': QueryBlogPathsResult;
    '\n  *[_type == "homePage" && _id == $id][0]{\n    \n  _id,\n  _type,\n  "title": select(\n    defined(ogTitle) => ogTitle,\n    defined(seoTitle) => seoTitle,\n    title\n  ),\n  "description": select(\n    defined(ogDescription) => ogDescription,\n    defined(seoDescription) => seoDescription,\n    description\n  ),\n  "image": image.asset->url + "?w=566&h=566&dpr=2&fit=max",\n  "dominantColor": image.asset->metadata.palette.dominant.background,\n  "seoImage": seoImage.asset->url + "?w=1200&h=630&dpr=2&fit=max", \n  "logo": *[_type == "settings"][0].logo.asset->url + "?w=80&h=40&dpr=3&fit=max&q=100",\n  "date": coalesce(date, _createdAt)\n\n  }\n  ': QueryHomePageOGDataResult;
    '\n  *[_type == "page" && _id == $id][0]{\n    \n  _id,\n  _type,\n  "title": select(\n    defined(ogTitle) => ogTitle,\n    defined(seoTitle) => seoTitle,\n    title\n  ),\n  "description": select(\n    defined(ogDescription) => ogDescription,\n    defined(seoDescription) => seoDescription,\n    description\n  ),\n  "image": image.asset->url + "?w=566&h=566&dpr=2&fit=max",\n  "dominantColor": image.asset->metadata.palette.dominant.background,\n  "seoImage": seoImage.asset->url + "?w=1200&h=630&dpr=2&fit=max", \n  "logo": *[_type == "settings"][0].logo.asset->url + "?w=80&h=40&dpr=3&fit=max&q=100",\n  "date": coalesce(date, _createdAt)\n\n  }\n': QuerySlugPageOGDataResult;
    '\n  *[_type == "blog" && _id == $id][0]{\n    \n  _id,\n  _type,\n  "title": select(\n    defined(ogTitle) => ogTitle,\n    defined(seoTitle) => seoTitle,\n    title\n  ),\n  "description": select(\n    defined(ogDescription) => ogDescription,\n    defined(seoDescription) => seoDescription,\n    description\n  ),\n  "image": image.asset->url + "?w=566&h=566&dpr=2&fit=max",\n  "dominantColor": image.asset->metadata.palette.dominant.background,\n  "seoImage": seoImage.asset->url + "?w=1200&h=630&dpr=2&fit=max", \n  "logo": *[_type == "settings"][0].logo.asset->url + "?w=80&h=40&dpr=3&fit=max&q=100",\n  "date": coalesce(date, _createdAt)\n\n  }\n': QueryBlogPageOGDataResult;
    '\n  *[ defined(slug.current) && _id == $id][0]{\n    \n  _id,\n  _type,\n  "title": select(\n    defined(ogTitle) => ogTitle,\n    defined(seoTitle) => seoTitle,\n    title\n  ),\n  "description": select(\n    defined(ogDescription) => ogDescription,\n    defined(seoDescription) => seoDescription,\n    description\n  ),\n  "image": image.asset->url + "?w=566&h=566&dpr=2&fit=max",\n  "dominantColor": image.asset->metadata.palette.dominant.background,\n  "seoImage": seoImage.asset->url + "?w=1200&h=630&dpr=2&fit=max", \n  "logo": *[_type == "settings"][0].logo.asset->url + "?w=80&h=40&dpr=3&fit=max&q=100",\n  "date": coalesce(date, _createdAt)\n\n  }\n': QueryGenericPageOGDataResult;
    '\n  *[_type == "footer" && _id == "footer"][0]{\n    _id,\n    subtitle,\n    columns[]{\n      _key,\n      title,\n      links[]{\n        _key,\n        name,\n        "openInNewTab": url.openInNewTab,\n        "href": select(\n          url.type == "internal" => url.internal->slug.current,\n          url.type == "external" => url.external,\n          url.href\n        ),\n      }\n    }\n  }\n': QueryFooterDataResult;
    '\n  *[_type == "navbar" && _id == "navbar"][0]{\n    _id,\n    columns[]{\n      _key,\n      _type == "navbarColumn" => {\n        "type": "column",\n        title,\n        links[]{\n          _key,\n          name,\n          icon,\n          description,\n          "openInNewTab": url.openInNewTab,\n          "href": select(\n            url.type == "internal" => url.internal->slug.current,\n            url.type == "external" => url.external,\n            url.href\n          )\n        }\n      },\n      _type == "navbarLink" => {\n        "type": "link",\n        name,\n        description,\n        "openInNewTab": url.openInNewTab,\n        "href": select(\n          url.type == "internal" => url.internal->slug.current,\n          url.type == "external" => url.external,\n          url.href\n        )\n      }\n    },\n    \n  buttons[]{\n    text,\n    variant,\n    _key,\n    _type,\n    "openInNewTab": url.openInNewTab,\n    "href": select(\n      url.type == "internal" => url.internal->slug.current,\n      url.type == "external" => url.external,\n      url.href\n    ),\n  }\n,\n  }\n': QueryNavbarDataResult;
    '{\n  "slugPages": *[_type == "page" && defined(slug.current)]{\n    "slug": slug.current,\n    "lastModified": _updatedAt\n  },\n  "blogPages": *[_type == "blog" && defined(slug.current)]{\n    "slug": slug.current,\n    "lastModified": _updatedAt\n  }\n}': QuerySitemapDataResult;
    '\n  *[_type == "settings"][0]{\n    _id,\n    _type,\n    siteTitle,\n    logo{\n      ...,\n      ...asset->{\n        "alt": coalesce(altText, originalFilename, "no-alt"),\n        "blurData": metadata.lqip,\n        "dominantColor": metadata.palette.dominant.background\n      }\n    },\n    siteDescription,\n    socialLinks{\n      linkedin,\n      facebook,\n      twitter,\n      instagram,\n      youtube\n    }\n  }\n': QueryGlobalSeoSettingsResult;
    '\n  *[_type == "settings"][0]{\n    _id,\n    _type,\n    siteTitle,\n    siteDescription,\n    "logo": logo.asset->url + "?w=80&h=40&dpr=3&fit=max",\n    "socialLinks": socialLinks,\n    "contactEmail": contactEmail,\n  }\n': QuerySettingsDataResult;
  }
}
