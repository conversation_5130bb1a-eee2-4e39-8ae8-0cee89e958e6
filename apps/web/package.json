{"name": "web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint .", "format": "prettier --write .", "check-types": "tsc --noEmit"}, "dependencies": {"@sanity/asset-utils": "^2.2.1", "@sanity/client": "^7.6.0", "@sanity/image-url": "^1.1.0", "@sanity/visual-editing": "^2.15.0", "@workspace/ui": "workspace:*", "babel-plugin-react-compiler": "19.1.0-rc.2", "lucide-react": "0.522.0", "next": "15.3.4", "next-sanity": "^9.12.0", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "schema-dts": "^1.1.5", "server-only": "^0.0.1", "slugify": "^1.6.6"}, "devDependencies": {"@types/node": "^24", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "postcss": "^8", "tailwindcss": "^3.4.17"}}