{"name": "studio", "private": true, "version": "1.0.0", "main": "package.json", "license": "UNLICENSED", "scripts": {"dev": "sanity dev", "format": "prettier --write .", "start": "sanity start", "lint": "eslint .", "build": "sanity build", "deploy": "sanity deploy", "type": "sanity schema extract --enforce-required-fields && sanity typegen generate", "check-types": "tsc --noEmit"}, "keywords": ["sanity"], "dependencies": {"@sanity/assist": "^4.3.2", "@sanity/icons": "^3.7.4", "@sanity/orderable-document-list": "^1.3.4", "@sanity/ui": "^2.16.2", "@sanity/vision": "^3.93.0", "lucide-react": "^0.522.0", "react": "19.1.0", "react-dom": "19.1.0", "sanity": "^3.93.0", "sanity-plugin-asset-source-unsplash": "^3.0.3", "sanity-plugin-icon-picker": "^4.0.0", "sanity-plugin-media": "^3.0.3", "slugify": "^1.6.6", "styled-components": "^6.1.19"}, "devDependencies": {"@faker-js/faker": "^9.8.0", "@portabletext/block-tools": "^1.1.31", "@sanity/eslint-config-studio": "^5.0.2", "@sanity/schema": "^3.93.0", "@supercharge/promise-pool": "^3.2.0", "@types/jsdom": "^21.1.7", "@types/node": "^24", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "eslint": "^9.29.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-simple-import-sort": "^12.1.1", "jsdom": "^26.1.0"}}