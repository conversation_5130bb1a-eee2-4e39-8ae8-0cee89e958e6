[{"name": "sanity.imagePaletteSwatch", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imagePaletteSwatch"}}, "background": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "foreground": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "population": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.imagePalette", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imagePalette"}}, "darkMuted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "lightVibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "darkVibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "vibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "dominant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "lightMuted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "muted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}}}}, {"name": "sanity.imageDimensions", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageDimensions"}}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "aspectRatio": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.fileAsset", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.fileAsset"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "originalFilename": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "altText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sha1hash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "extension": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "mimeType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "assetId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "uploadId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.assetSourceData"}, "optional": true}}}, {"name": "geopoint", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "geopoint"}}, "lat": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "lng": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "subscribeNewsletter", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "subscribeNewsletter"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "subTitle": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "inline"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "number"}, {"type": "string", "value": "bullet"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"customLink": {"type": "objectAttribute", "value": {"type": "inline", "name": "customUrl"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "customLink"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "helperText": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "inline"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "number"}, {"type": "string", "value": "bullet"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"customLink": {"type": "objectAttribute", "value": {"type": "inline", "name": "customUrl"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "customLink"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}}, {"name": "imageLinkCards", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "imageLinkCards"}}, "eyebrow": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": false}, "richText": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "inline"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "number"}, {"type": "string", "value": "bullet"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"customLink": {"type": "objectAttribute", "value": {"type": "inline", "name": "customUrl"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "customLink"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "caption": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}]}}, "optional": true}, "buttons": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "button"}}}, "optional": true}, "cards": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": false}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": false}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "inline", "name": "customUrl"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "imageLinkCard"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}}, {"name": "faqAccordion", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "faqAccordion"}}, "eyebrow": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": false}, "subtitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "link": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "inline", "name": "customUrl"}, "optional": true}}}, "optional": true}, "faqs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "faq", "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": false}}}}, {"name": "featureCardsIcon", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "featureCardsIcon"}}, "eyebrow": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "richText": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "inline"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "number"}, {"type": "string", "value": "bullet"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"customLink": {"type": "objectAttribute", "value": {"type": "inline", "name": "customUrl"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "customLink"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "cards": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"icon": {"type": "objectAttribute", "value": {"type": "inline", "name": "iconPicker"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "richText": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "inline"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "number"}, {"type": "string", "value": "bullet"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"customLink": {"type": "objectAttribute", "value": {"type": "inline", "name": "customUrl"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "customLink"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "featureCardIcon"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}}, {"name": "cta", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "cta"}}, "eyebrow": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "richText": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "inline"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "number"}, {"type": "string", "value": "bullet"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"customLink": {"type": "objectAttribute", "value": {"type": "inline", "name": "customUrl"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "customLink"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "caption": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}]}}, "optional": true}, "buttons": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "button"}}}, "optional": true}}}}, {"name": "hero", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "hero"}}, "badge": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "richText": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "inline"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "number"}, {"type": "string", "value": "bullet"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"customLink": {"type": "objectAttribute", "value": {"type": "inline", "name": "customUrl"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "customLink"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "caption": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}]}}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "buttons": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "button"}}}, "optional": true}}}}, {"name": "pageBuilder", "type": "type", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "hero"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "cta"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "featureCardsIcon"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "faqAccordion"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "imageLinkCards"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "subscribeNewsletter"}}]}}}, {"name": "button", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "button"}}, "variant": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "default"}, {"type": "string", "value": "secondary"}, {"type": "string", "value": "outline"}, {"type": "string", "value": "link"}]}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "inline", "name": "customUrl"}, "optional": true}}}}, {"name": "richText", "type": "type", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "inline"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "number"}, {"type": "string", "value": "bullet"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"customLink": {"type": "objectAttribute", "value": {"type": "inline", "name": "customUrl"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "customLink"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "caption": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}]}}}, {"name": "navbar", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "navbar"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": false}, "columns": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "links": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"icon": {"type": "objectAttribute", "value": {"type": "inline", "name": "iconPicker"}, "optional": true}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "inline", "name": "customUrl"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "navbarColumnLink"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": false}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "navbarColumn"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "inline", "name": "customUrl"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "navbarLink"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}]}}, "optional": true}, "buttons": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "button"}}}, "optional": true}}}, {"name": "footer", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "footer"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": false}, "subtitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "columns": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "links": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "inline", "name": "customUrl"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "footerColumnLink"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "footerColumn"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}, {"name": "settings", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "settings"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": false}, "siteTitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": false}, "siteDescription": {"type": "objectAttribute", "value": {"type": "string"}, "optional": false}, "logo": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "contactEmail": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "socialLinks": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"linkedin": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "facebook": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "twitter": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "instagram": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "youtube": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}}}, {"name": "blogIndex", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "blogIndex"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": false}, "displayFeaturedBlogs": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "yes"}, {"type": "string", "value": "no"}]}, "optional": true}, "featuredBlogsCount": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "1"}, {"type": "string", "value": "2"}, {"type": "string", "value": "3"}]}, "optional": true}, "pageBuilder": {"type": "objectAttribute", "value": {"type": "inline", "name": "pageBuilder"}, "optional": true}, "seoTitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "seoDescription": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "seoImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "ogTitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "ogDescription": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, {"name": "homePage", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "homePage"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": false}, "pageBuilder": {"type": "objectAttribute", "value": {"type": "inline", "name": "pageBuilder"}, "optional": true}, "seoTitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "seoDescription": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "seoImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "ogTitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "ogDescription": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, {"name": "author", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "author"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": false}, "position": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "bio": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, {"name": "faq", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "faq"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": false}, "richText": {"type": "objectAttribute", "value": {"type": "inline", "name": "richText"}, "optional": true}}}, {"name": "page", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "page"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": false}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": false}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "pageBuilder": {"type": "objectAttribute", "value": {"type": "inline", "name": "pageBuilder"}, "optional": true}, "seoTitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "seoDescription": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "seoImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "seoNoIndex": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "ogTitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "ogDescription": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, {"name": "blog", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "blog"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "orderRank": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": false}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": false}, "authors": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "author", "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": false}, "publishedAt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": false}, "richText": {"type": "objectAttribute", "value": {"type": "inline", "name": "richText"}, "optional": true}, "seoTitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "seoDescription": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "seoImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "seoNoIndex": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "seoHideFromLists": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "ogTitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "ogDescription": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, {"name": "customUrl", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "customUrl"}}, "type": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "internal"}, {"type": "string", "value": "external"}]}, "optional": false}, "openInNewTab": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "external": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "internal": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "blog"}, {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "blogIndex"}, {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "page"}]}, "optional": true}}}}, {"name": "sanity.imageCrop", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageCrop"}}, "top": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "bottom": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "left": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "right": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.imageHotspot", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageHotspot"}}, "x": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "y": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.imageAsset", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageAsset"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "originalFilename": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "altText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sha1hash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "extension": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "mimeType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "assetId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "uploadId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "metadata": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageMetadata"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.assetSourceData"}, "optional": true}}}, {"name": "sanity.assetSourceData", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.assetSourceData"}}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "id": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.imageMetadata", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageMetadata"}}, "location": {"type": "objectAttribute", "value": {"type": "inline", "name": "geopoint"}, "optional": true}, "dimensions": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageDimensions"}, "optional": true}, "palette": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePalette"}, "optional": true}, "lqip": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "blurHash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "hasAlpha": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "isOpaque": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}}, {"name": "media.tag", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "media.tag"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "name": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}}}, {"name": "slug", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "slug"}}, "current": {"type": "objectAttribute", "value": {"type": "string"}, "optional": false}, "source": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "iconPicker", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "iconPicker"}}, "provider": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "svg": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.assist.instructionTask", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.assist.instructionTask"}}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "instructionKey": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "started": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "updated": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "info": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.assist.task.status", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.assist.task.status"}}, "tasks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "sanity.assist.instructionTask"}}}, "optional": true}}}}, {"name": "sanity.assist.schemaType.annotations", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.assist.schemaType.annotations"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "fields": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "sanity.assist.schemaType.field"}}}, "optional": true}}}}, {"name": "sanity.assist.output.type", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.assist.output.type"}}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.assist.output.field", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.assist.output.field"}}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.assist.instruction.context", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.assist.instruction.context"}}, "reference": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "assist.instruction.context"}, "optional": false}}}}, {"name": "assist.instruction.context", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "assist.instruction.context"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "context": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": []}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "null"}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}, {"name": "sanity.assist.instruction.userInput", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.assist.instruction.userInput"}}, "message": {"type": "objectAttribute", "value": {"type": "string"}, "optional": false}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.assist.instruction.prompt", "type": "type", "value": {"type": "array", "of": {"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "sanity.assist.instruction.fieldRef"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "sanity.assist.instruction.context"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "sanity.assist.instruction.userInput"}}]}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": []}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "null"}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}}, {"name": "sanity.assist.instruction.fieldRef", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.assist.instruction.fieldRef"}}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.assist.instruction", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.assist.instruction"}}, "prompt": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.assist.instruction.prompt"}, "optional": true}, "icon": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "userId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "createdById": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "output": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "sanity.assist.output.field"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "sanity.assist.output.type"}}]}}, "optional": true}}}}, {"name": "sanity.assist.schemaType.field", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.assist.schemaType.field"}}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "instructions": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "sanity.assist.instruction"}}}, "optional": true}}}}]