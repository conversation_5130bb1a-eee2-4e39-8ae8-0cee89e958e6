export function Logo() {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <title>Turbo Start</title>
      <rect width="32" height="32" rx="16" fill="var(--card-fg-color)" />
      <path
        d="M4.68629 21.159C2.98673 19.4595 2.98673 16.704 4.68629 15.0044L12.1081 7.5826L18.2627 13.7373L10.841 21.159C9.14139 22.8586 6.38585 22.8586 4.68629 21.159Z"
        fill="url(#paint0_linear_2_18)"
      />
      <path
        d="M27.3137 10.841C25.6142 9.14139 22.8586 9.14139 21.1591 10.841L13.7373 18.2627L19.8919 24.4174L27.3137 16.9956C29.0133 15.2961 29.0133 12.5405 27.3137 10.841Z"
        fill="url(#paint1_linear_2_18)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_2_18"
          x1="14.9316"
          y1="10.9137"
          x2="4.68629"
          y2="21.159"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="var(--card-fg-color)" />
          <stop offset="1" stopColor="var(--card-bg-color)" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_2_18"
          x1="17.0684"
          y1="21.0863"
          x2="27.3137"
          y2="10.841"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="var(--card-fg-color)" />
          <stop offset="1" stopColor="var(--card-bg-color)" />
        </linearGradient>
      </defs>
    </svg>
  );
}
