{"name": "next-sanity-base", "version": "1.0.0", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "check-types": "turbo check-types", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "prettier": "^3.6.0", "turbo": "^2.5.4", "typescript": "5.7.3"}, "packageManager": "pnpm@10.12.2", "engines": {"node": ">=20"}}